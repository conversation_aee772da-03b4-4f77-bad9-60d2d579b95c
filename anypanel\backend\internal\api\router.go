package api

import (
	"anytls-panel/internal/config"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

// NewRouter 创建新的路由器
func NewRouter(cfg *config.Config, db *gorm.DB, rdb *redis.Client) *gin.Engine {
	router := gin.New()

	// 中间件
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// CORS中间件
	router.Use(func(c *gin.Context) {
		c.<PERSON>er("Access-Control-Allow-Origin", "*")
		c.<PERSON><PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.<PERSON>("Access-Control-Allow-Headers", "Content-Type, Authorization")
		
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}
		
		c.Next()
	})

	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		c.<PERSON>SON(200, gin.H{
			"status": "ok",
			"message": "AnyTLS Panel is running",
		})
	})

	// API路由组
	v1 := router.Group("/api/v1")
	{
		// 认证相关路由
		auth := v1.Group("/auth")
		{
			auth.POST("/login", func(c *gin.Context) {
				c.JSON(200, gin.H{"message": "Login endpoint - TODO"})
			})
			auth.POST("/register", func(c *gin.Context) {
				c.JSON(200, gin.H{"message": "Register endpoint - TODO"})
			})
		}

		// 管理员路由
		admin := v1.Group("/admin")
		{
			admin.GET("/users", func(c *gin.Context) {
				c.JSON(200, gin.H{"message": "Admin users endpoint - TODO"})
			})
			admin.GET("/nodes", func(c *gin.Context) {
				c.JSON(200, gin.H{"message": "Admin nodes endpoint - TODO"})
			})
		}

		// 用户路由
		user := v1.Group("/user")
		{
			user.GET("/profile", func(c *gin.Context) {
				c.JSON(200, gin.H{"message": "User profile endpoint - TODO"})
			})
			user.GET("/subscription", func(c *gin.Context) {
				c.JSON(200, gin.H{"message": "User subscription endpoint - TODO"})
			})
		}

		// V2bX兼容API路由
		server := v1.Group("/server/UniProxy")
		{
			server.GET("/config", func(c *gin.Context) {
				c.JSON(200, gin.H{"message": "V2bX config endpoint - TODO"})
			})
			server.GET("/user", func(c *gin.Context) {
				c.JSON(200, gin.H{"message": "V2bX user list endpoint - TODO"})
			})
			server.POST("/push", func(c *gin.Context) {
				c.JSON(200, gin.H{"message": "V2bX traffic push endpoint - TODO"})
			})
		}
	}

	return router
}