package model

import (
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// User 用户模型
type User struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	UUID         string         `json:"uuid" gorm:"uniqueIndex;size:36"`
	Username     string         `json:"username" gorm:"uniqueIndex;size:50"`
	Email        string         `json:"email" gorm:"uniqueIndex;size:100"`
	PasswordHash string         `json:"-" gorm:"size:255"`
	Role         string         `json:"role" gorm:"type:enum('admin','user');default:'user'"`
	Status       string         `json:"status" gorm:"type:enum('active','inactive','expired');default:'active'"`
	TrafficLimit int64          `json:"traffic_limit" gorm:"default:0"`
	TrafficUsed  int64          `json:"traffic_used" gorm:"default:0"`
	DeviceLimit  int            `json:"device_limit" gorm:"default:1"`
	SpeedLimit   int            `json:"speed_limit" gorm:"default:0"`
	ExpiredAt    *time.Time     `json:"expired_at"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Nodes             []Node             `json:"nodes" gorm:"many2many:user_nodes;"`
	UserSubscriptions []UserSubscription `json:"user_subscriptions" gorm:"foreignKey:UserID"`
	Orders            []Order            `json:"orders" gorm:"foreignKey:UserID"`
}

// Node 节点模型
type Node struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	Name        string         `json:"name" gorm:"size:100"`
	Protocol    string         `json:"protocol" gorm:"size:20;default:'anytls'"`
	Host        string         `json:"host" gorm:"size:255"`
	Port        int            `json:"port"`
	Password    string         `json:"password" gorm:"size:255"`
	Config      datatypes.JSON `json:"config"` // 协议特定配置
	ServerName  string         `json:"server_name" gorm:"size:255"`
	Status      string         `json:"status" gorm:"type:enum('online','offline','maintenance');default:'offline'"`
	SortOrder   int            `json:"sort_order" gorm:"default:0"`
	TrafficRate float64        `json:"traffic_rate" gorm:"type:decimal(10,2);default:1.0"`
	MaxUsers    int            `json:"max_users" gorm:"default:0"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Users []User `json:"users" gorm:"many2many:user_nodes;"`
}

// PermissionGroup 权限组模型
type PermissionGroup struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	Name        string         `json:"name" gorm:"uniqueIndex;size:100"`
	Description string         `json:"description" gorm:"type:text"`
	SortOrder   int            `json:"sort_order" gorm:"default:0"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Nodes    []Node    `json:"nodes" gorm:"many2many:permission_group_nodes;"`
	Products []Product `json:"products" gorm:"foreignKey:PermissionGroupID"`
}

// Product 订阅商品模型
type Product struct {
	ID                  uint           `json:"id" gorm:"primaryKey"`
	Name                string         `json:"name" gorm:"size:100"`
	Description         string         `json:"description" gorm:"type:text"`
	Price               float64        `json:"price" gorm:"type:decimal(10,2)"`
	TrafficLimit        int64          `json:"traffic_limit"`
	DurationDays        int            `json:"duration_days"`
	DeviceLimit         int            `json:"device_limit" gorm:"default:1"`
	SpeedLimit          int            `json:"speed_limit" gorm:"default:0"`
	PermissionGroupID   uint           `json:"permission_group_id"`
	Status              string         `json:"status" gorm:"type:enum('active','inactive');default:'active'"`
	SortOrder           int            `json:"sort_order" gorm:"default:0"`
	CreatedAt           time.Time      `json:"created_at"`
	UpdatedAt           time.Time      `json:"updated_at"`
	DeletedAt           gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	PermissionGroup   PermissionGroup    `json:"permission_group" gorm:"foreignKey:PermissionGroupID"`
	UserSubscriptions []UserSubscription `json:"user_subscriptions" gorm:"foreignKey:ProductID"`
	Orders            []Order            `json:"orders" gorm:"foreignKey:ProductID"`
}

// UserSubscription 用户订阅模型
type UserSubscription struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	UserID    uint           `json:"user_id"`
	ProductID uint           `json:"product_id"`
	OrderID   string         `json:"order_id" gorm:"size:50"`
	Status    string         `json:"status" gorm:"type:enum('active','expired','cancelled');default:'active'"`
	StartedAt time.Time      `json:"started_at"`
	ExpiredAt time.Time      `json:"expired_at"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	User    User    `json:"user" gorm:"foreignKey:UserID"`
	Product Product `json:"product" gorm:"foreignKey:ProductID"`
}

// Order 订单模型
type Order struct {
	ID            uint           `json:"id" gorm:"primaryKey"`
	OrderID       string         `json:"order_id" gorm:"uniqueIndex;size:50"`
	UserID        uint           `json:"user_id"`
	ProductID     uint           `json:"product_id"`
	Amount        float64        `json:"amount" gorm:"type:decimal(10,2)"`
	Status        string         `json:"status" gorm:"type:enum('pending','paid','cancelled','refunded');default:'pending'"`
	PaymentMethod string         `json:"payment_method" gorm:"size:50"`
	PaymentID     string         `json:"payment_id" gorm:"size:100"`
	PaidAt        *time.Time     `json:"paid_at"`
	CreatedAt     time.Time      `json:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at"`
	DeletedAt     gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	User    User    `json:"user" gorm:"foreignKey:UserID"`
	Product Product `json:"product" gorm:"foreignKey:ProductID"`
}

// TrafficLog 流量统计模型
type TrafficLog struct {
	ID         uint      `json:"id" gorm:"primaryKey"`
	UserID     uint      `json:"user_id"`
	NodeID     uint      `json:"node_id"`
	Upload     int64     `json:"upload" gorm:"default:0"`
	Download   int64     `json:"download" gorm:"default:0"`
	RecordedAt time.Time `json:"recorded_at"`

	// 关联关系
	User User `json:"user" gorm:"foreignKey:UserID"`
	Node Node `json:"node" gorm:"foreignKey:NodeID"`
}

// OnlineUser 在线用户模型
type OnlineUser struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	UserID      uint      `json:"user_id"`
	NodeID      uint      `json:"node_id"`
	IPAddress   string    `json:"ip_address" gorm:"size:45"`
	ConnectedAt time.Time `json:"connected_at"`
	LastSeen    time.Time `json:"last_seen"`

	// 关联关系
	User User `json:"user" gorm:"foreignKey:UserID"`
	Node Node `json:"node" gorm:"foreignKey:NodeID"`
}