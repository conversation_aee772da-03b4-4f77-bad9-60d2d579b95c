# AnyPanel

一个专门为AnyTLS协议设计的多用户管理面板，支持商品化订阅服务。

## 项目特点

- 🎯 **专注AnyTLS协议**：当前版本专门为AnyTLS协议优化
- 🏗️ **可扩展架构**：支持未来添加其他代理协议
- 🛍️ **商品化系统**：完整的订阅商品和权限组管理
- 📱 **多客户端支持**：支持Clash、Sing-box、V2Ray等主流客户端
- 🔒 **权限控制**：基于权限组的精细化节点访问控制
- 💰 **商业化就绪**：内置订单、支付和订阅管理系统

## 技术栈

### 后端
- Go 1.21+
- Gin Web框架
- GORM数据库ORM
- JWT身份认证
- MySQL 8.0
- Redis 7.0

### 前端
- React 18 + TypeScript
- Ant Design Pro
- React Query
- Zustand状态管理
- Ant Design Charts

## 项目结构

```
anypanel/
├── backend/                 # Go后端服务
│   ├── cmd/                # 应用入口
│   ├── internal/           # 内部包
│   │   ├── api/           # API路由和控制器
│   │   ├── service/       # 业务逻辑层
│   │   ├── model/         # 数据模型
│   │   ├── middleware/    # 中间件
│   │   └── config/        # 配置管理
│   ├── migrations/         # 数据库迁移
│   ├── scripts/           # 脚本文件
│   └── go.mod
├── frontend/               # React前端应用
│   ├── src/
│   │   ├── components/    # 通用组件
│   │   ├── pages/         # 页面组件
│   │   ├── services/      # API服务
│   │   ├── utils/         # 工具函数
│   │   ├── hooks/         # 自定义Hooks
│   │   ├── stores/        # 状态管理
│   │   └── types/         # TypeScript类型
│   ├── public/
│   └── package.json
├── docs/                   # 项目文档
├── scripts/               # 开发脚本
└── README.md
```

## 快速开始

### 环境要求

- Go 1.21+
- Node.js 18+
- MySQL 8.0+
- Redis 7.0+

### 开发环境设置

1. 启动后端服务
```bash
cd backend
go mod tidy
go run cmd/main.go
```

2. 启动前端服务
```bash
cd frontend
npm install
npm start
```

## 开发指南

详细的开发指南请参考 [docs/](./docs/) 目录下的文档。

## 许可证

MIT License